import { ethers } from 'ethers';
import { toast } from 'sonner';

// Import contract ABIs from the contractABI folder
import WasteVanABI from '../../contractABI/WasteVan.json';
import WasteVanTokenABI from '../../contractABI/WasteVanToken.json';

// Contract addresses - these should be updated with your deployed contract addresses
// You can set these as environment variables in a .env file
const WASTE_VAN_TOKEN_ADDRESS = import.meta.env.VITE_WASTE_VAN_TOKEN_ADDRESS;
const WASTE_VAN_ADDRESS = import.meta.env.VITE_WASTE_VAN_ADDRESS;

export const getContract = async () => {
  if (typeof window.ethereum === 'undefined') {
    toast.error('Please install MetaMask to interact with the blockchain');
    throw new Error('Please install MetaMask');
  }

  try {
    const provider = new ethers.BrowserProvider(window.ethereum);
    const signer = await provider.getSigner();

    // Check if we're on the correct network
    const network = await provider.getNetwork();
    const chainId = Number(network.chainId);

    // Sepolia testnet chainId is 11155111
    // You can modify this to check for your target network
    if (chainId !== 11155111 && chainId !== 1337 && chainId !== 31337) {
      toast.warning('Please switch to Sepolia testnet to use this application');
    }

    const wasteVanToken = new ethers.Contract(
      WASTE_VAN_TOKEN_ADDRESS,
      WasteVanTokenABI.abi,
      signer
    );

    const wasteVan = new ethers.Contract(
      WASTE_VAN_ADDRESS,
      WasteVanABI.abi,
      signer
    );

    return { wasteVanToken, wasteVan, signer, provider };
  } catch (error) {
    console.error('Error getting contracts:', error);
    toast.error('Failed to connect to blockchain. Please check your wallet connection.');
    throw error;
  }
};

export const registerUser = async (username: string, email: string) => {
  try {
    toast.loading('Registering user...');
    const { wasteVan } = await getContract();
    const tx = await wasteVan.registerUser(username, email);
    toast.loading(`Transaction submitted. Waiting for confirmation...`);
    await tx.wait();
    toast.success('User registered successfully!');
    return tx;
  } catch (error) {
    console.error('Error registering user:', error);
    toast.error('Failed to register user. Please try again.');
    throw error;
  }
};

export const registerAgent = async () => {
  try {
    toast.loading('Registering as agent...');
    const { wasteVan } = await getContract();
    const tx = await wasteVan.registerAgent();
    toast.loading(`Transaction submitted. Waiting for confirmation...`);
    await tx.wait();
    toast.success('Registered as agent successfully! You received 1000 WVT tokens.');
    return tx;
  } catch (error) {
    console.error('Error registering as agent:', error);
    toast.error('Failed to register as agent. Please try again.');
    throw error;
  }
};
