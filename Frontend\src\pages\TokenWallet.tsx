
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { formatAddress } from '@/utils/web3Utils';
import { toast } from 'sonner';
import Footer from '@/components/Footer';
import { Wallet, ArrowUpRight, Recycle, Award, BarChart2, Users } from 'lucide-react';
import { getTokenBalance, getUserWasteReports, getAgentStats, getAgentCollectedReports } from '@/utils/contracts';
import { ethers } from 'ethers';
import { useContract } from '@/context/ContractContext';

interface Transaction {
  id: number;
  type: 'Earned' | 'Sent' | 'Received';
  amount: number;
  timestamp: number;
  address: string;
  description: string;
}

interface CollectedWaste {
  id: number;
  reporter: string;
  quantity: number;
  wasteType: string;
  timestamp: number;
  tokenReward: number;
}

const TokenWallet: React.FC = () => {
  const { account, connectWallet, isAgent, agentStats, disconnectWallet, tokenBalance: contextTokenBalance } = useContract();
  const [tokenBalance, setTokenBalance] = useState<number | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [collectedWaste, setCollectedWaste] = useState<CollectedWaste[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalWasteCollected, setTotalWasteCollected] = useState<number>(0);
  const [isConnecting, setIsConnecting] = useState(false);

  useEffect(() => {
    if (account) {
      fetchWalletData();
    } else {
      setLoading(false);
    }
  }, [account, isAgent]);

  useEffect(() => {
    // Update local token balance from context when it changes
    if (contextTokenBalance) {
      setTokenBalance(parseFloat(contextTokenBalance));
    }
  }, [contextTokenBalance]);

  const handleConnectWallet = async () => {
    try {
      setIsConnecting(true);
      await connectWallet();
    } catch (error) {
      console.error("Error connecting wallet:", error);
      toast.error("Failed to connect wallet");
    } finally {
      setIsConnecting(false);
    }
  };

  const fetchWalletData = async () => {
    if (!account) return;

    setLoading(true);
    try {
      // Get token balance from blockchain
      const balanceWei = await getTokenBalance(account);
      const balanceEther = parseFloat(ethers.formatEther(balanceWei));
      setTokenBalance(balanceEther);

      // Get waste reports from blockchain
      await fetchWasteReports(account);

      // If user is an agent, fetch collected waste reports
      if (isAgent) {
        await fetchAgentCollections(account);
      }
    } catch (error) {
      console.error("Error fetching wallet data:", error);
      toast.error("Failed to load wallet data from blockchain");
    } finally {
      setLoading(false);
    }
  };

  const fetchWasteReports = async (address: string) => {
    try {
      const reports = await getUserWasteReports(address);

      // Convert waste reports to transaction format
      const txs: Transaction[] = reports.map(report => {
        return {
          id: Number(report.reportId),
          type: 'Earned',
          amount: Number(ethers.formatEther(report.tokenReward)),
          timestamp: Number(report.timestamp) * 1000, // Convert from seconds to milliseconds
          address: report.collectedBy || "******************************************",
          description: `Waste report: ${report.wasteType} (${report.quantity} kg)${report.isCollected ? ' - Collected' : ' - Pending'}`
        };
      });

      // Sort by timestamp (newest first)
      txs.sort((a, b) => b.timestamp - a.timestamp);

      setTransactions(txs);
    } catch (error) {
      console.error("Error fetching waste reports:", error);
      toast.error("Failed to load waste reports from blockchain");
    }
  };

  const fetchAgentCollections = async (address: string) => {
    try {
      const reports = await getAgentCollectedReports(address);

      // Calculate total waste collected
      const totalWaste = reports.reduce((sum, report) => sum + Number(report.quantity), 0);
      setTotalWasteCollected(totalWaste);

      // Convert to CollectedWaste format
      const collections: CollectedWaste[] = reports.map(report => {
        return {
          id: Number(report.reportId),
          reporter: report.reporter,
          quantity: Number(report.quantity),
          wasteType: report.wasteType,
          timestamp: Number(report.timestamp),
          tokenReward: Number(ethers.formatEther(report.tokenReward))
        };
      });

      // Sort by timestamp (newest first)
      collections.sort((a, b) => b.timestamp - a.timestamp);

      setCollectedWaste(collections);
    } catch (error) {
      console.error("Error fetching agent collections:", error);
      toast.error("Failed to load agent collections from blockchain");
    }
  };

  const formatDate = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
        <div className="mb-12">
          <h1 className="text-3xl font-extrabold text-gray-900 dark:text-white sm:text-4xl">
            {isAgent ? 'Agent Wallet' : 'Token Wallet'}
          </h1>
          <p className="mt-3 max-w-2xl text-xl text-gray-500 dark:text-gray-400">
            {isAgent
              ? 'Manage your WasteVan tokens, view your agent stats, and track collected waste.'
              : 'Manage your WasteVan tokens and view your transaction history.'}
          </p>
        </div>

        {!account ? (
          <div className="text-center max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-10">
            <Wallet className="h-16 w-16 mx-auto text-waste-600 dark:text-waste-400 mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Connect Your Wallet
            </h2>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              Please connect your Ethereum wallet to view your tokens and transaction history.
            </p>
            <Button
              onClick={handleConnectWallet}
              disabled={isConnecting}
              className="bg-waste-600 hover:bg-waste-700 text-white"
            >
              {isConnecting ? 'Connecting...' : 'Connect Wallet'}
            </Button>
          </div>
        ) : (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader className="bg-waste-50 dark:bg-waste-900">
                  <CardTitle>Wallet Balance</CardTitle>
                  <CardDescription>Your current WasteVan token balance</CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  {loading ? (
                    <div className="animate-pulse h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  ) : (
                    <div className="flex items-center">
                      <div className="mr-4 p-3 bg-waste-100 dark:bg-waste-800 rounded-full">
                        <Wallet className="h-8 w-8 text-waste-600 dark:text-waste-400" />
                      </div>
                      <div>
                        <div className="text-3xl font-bold text-waste-600 dark:text-waste-400">
                          {tokenBalance?.toLocaleString()} WVT
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          Connected: {formatAddress(account)}
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {isAgent && agentStats && (
                <Card>
                  <CardHeader className="bg-waste-50 dark:bg-waste-900">
                    <CardTitle>Agent Stats</CardTitle>
                    <CardDescription>Your performance as a waste collection agent</CardDescription>
                  </CardHeader>
                  <CardContent className="pt-6">
                    {loading ? (
                      <div className="animate-pulse h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    ) : (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                          <div className="flex items-center mb-2">
                            <Users className="h-5 w-5 text-waste-600 dark:text-waste-400 mr-2" />
                            <span className="text-sm text-gray-500 dark:text-gray-400">Collections</span>
                          </div>
                          <div className="text-2xl font-bold text-waste-600 dark:text-waste-400">
                            {agentStats.totalCollections?.toString() || '0'}
                          </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                          <div className="flex items-center mb-2">
                            <Award className="h-5 w-5 text-waste-600 dark:text-waste-400 mr-2" />
                            <span className="text-sm text-gray-500 dark:text-gray-400">Points</span>
                          </div>
                          <div className="text-2xl font-bold text-waste-600 dark:text-waste-400">
                            {agentStats.points?.toString() || '0'}
                          </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                          <div className="flex items-center mb-2">
                            <Recycle className="h-5 w-5 text-waste-600 dark:text-waste-400 mr-2" />
                            <span className="text-sm text-gray-500 dark:text-gray-400">Waste Collected</span>
                          </div>
                          <div className="text-2xl font-bold text-waste-600 dark:text-waste-400">
                            {totalWasteCollected.toFixed(2)} kg
                          </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                          <div className="flex items-center mb-2">
                            <BarChart2 className="h-5 w-5 text-waste-600 dark:text-waste-400 mr-2" />
                            <span className="text-sm text-gray-500 dark:text-gray-400">Impact</span>
                          </div>
                          <div className="text-2xl font-bold text-waste-600 dark:text-waste-400">
                            {(totalWasteCollected * 3 / 1000).toFixed(2)} t CO₂
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardHeader className="bg-waste-50 dark:bg-waste-900">
                  <CardTitle>Token Actions</CardTitle>
                  <CardDescription>Manage your tokens and view blockchain data</CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      onClick={fetchWalletData}
                      disabled={loading}
                      className="bg-waste-600 hover:bg-waste-700 text-white"
                    >
                      {loading ? 'Refreshing...' : 'Refresh Data'}
                    </Button>
                    <Button
                      variant="outline"
                      className="border-waste-500 text-waste-700 hover:bg-waste-50 hover:text-waste-800"
                      onClick={() => {
                        if (account) {
                          // Open etherscan or similar explorer based on network
                          window.open(`https://sepolia.etherscan.io/address/${account}`, '_blank');
                        }
                      }}
                    >
                      View on Explorer <ArrowUpRight className="h-4 w-4 ml-2" />
                    </Button>
                    <Button
                      onClick={disconnectWallet}
                      className="col-span-2 bg-red-600 hover:bg-red-700 text-white"
                    >
                      Disconnect Wallet
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {isAgent && (
              <Card>
                <CardHeader className="bg-waste-50 dark:bg-waste-900">
                  <CardTitle>Collected Waste</CardTitle>
                  <CardDescription>Waste reports you have collected and verified</CardDescription>
                </CardHeader>
                <CardContent className="p-0">
                  {loading ? (
                    <div className="p-6 space-y-3">
                      {[1, 2, 3].map((item) => (
                        <div key={item} className="animate-pulse h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      ))}
                    </div>
                  ) : collectedWaste.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Reporter</TableHead>
                          <TableHead>Waste Type</TableHead>
                          <TableHead>Quantity (kg)</TableHead>
                          <TableHead className="text-right">Reward (WVT)</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {collectedWaste.map((waste) => (
                          <TableRow key={waste.id}>
                            <TableCell>{formatDate(waste.timestamp)}</TableCell>
                            <TableCell>{formatAddress(waste.reporter)}</TableCell>
                            <TableCell>{waste.wasteType}</TableCell>
                            <TableCell>{waste.quantity.toFixed(2)}</TableCell>
                            <TableCell className="text-right font-medium">
                              <span className="text-green-600 dark:text-green-400">
                                {waste.tokenReward.toFixed(2)}
                              </span>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-12">
                      <p className="text-gray-500 dark:text-gray-400">
                        No waste collections found. Start collecting waste reports from the Agent Dashboard!
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader className="bg-waste-50 dark:bg-waste-900">
                <CardTitle>Waste Reports & Rewards</CardTitle>
                <CardDescription>Your waste reports and token rewards from the blockchain</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                {loading ? (
                  <div className="p-6 space-y-3">
                    {[1, 2, 3].map((item) => (
                      <div key={item} className="animate-pulse h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    ))}
                  </div>
                ) : transactions.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead className="text-right">Tokens (WVT)</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {transactions.map((tx) => (
                        <TableRow key={tx.id}>
                          <TableCell>{formatDate(tx.timestamp)}</TableCell>
                          <TableCell>
                            <span
                              className={`inline-block px-2 py-1 text-xs rounded-full
                                ${tx.description.includes('Collected') ?
                                  'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                  'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                                }`}
                            >
                              {tx.description.includes('Collected') ? 'Collected' : 'Pending'}
                            </span>
                          </TableCell>
                          <TableCell>{tx.description}</TableCell>
                          <TableCell className="text-right font-medium">
                            <span className="text-green-600 dark:text-green-400">
                              {tx.amount.toFixed(2)}
                            </span>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-gray-500 dark:text-gray-400">
                      No waste reports found. Start reporting waste to earn tokens!
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </div>
      <Footer />
    </div>
  );
};

export default TokenWallet;
